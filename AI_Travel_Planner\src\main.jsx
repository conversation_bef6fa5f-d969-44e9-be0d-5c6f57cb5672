import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { createBrowserRouter, RouterProvider } from 'react-router-dom'
import CreateTrip from './create-trip/index.jsx'
import Header from './components/custom/Header.jsx'
const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <>
        <Header/>
        <App/>
      </>
    ),
  },
  {
    path: '/create-trip',
    element: (
      <>
        <Header/>
        <CreateTrip/>
      </>
    )
  }
]);

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
);
